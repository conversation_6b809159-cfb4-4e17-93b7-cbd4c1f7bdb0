import { useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  AppsIcon,
  StarIcon,
  ClockIcon,
  DownloadIcon,
  PlayIcon,
  GraphIcon,
  TrophyIcon,
  ChevronRightIcon,
} from "@primer/octicons-react";

import type { LibraryGame } from "@types";
import { useLibrary, useLibraryCollections } from "@renderer/hooks";
import { wasPlayedRecently } from "@renderer/utils/date-utils";
import "./sidebar-library-insights.scss";

// Utility function to format play time
function formatPlayTime(milliseconds: number): string {
  if (!milliseconds || milliseconds === 0) return "0m";

  const hours = Math.floor(milliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}

interface InsightCardProps {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  subtitle?: string;
  color: string;
  onClick?: () => void;
  clickable?: boolean;
}

function InsightCard({ 
  icon, 
  title, 
  value, 
  subtitle, 
  color, 
  onClick, 
  clickable = false 
}: InsightCardProps) {
  return (
    <button
      type="button"
      className={`sidebar-library-insights__card ${
        clickable ? "sidebar-library-insights__card--clickable" : ""
      }`}
      onClick={onClick}
      disabled={!clickable}
    >
      <div 
        className="sidebar-library-insights__card-icon"
        style={{ backgroundColor: color }}
      >
        {icon}
      </div>
      <div className="sidebar-library-insights__card-content">
        <div className="sidebar-library-insights__card-value">{value}</div>
        <div className="sidebar-library-insights__card-title">{title}</div>
        {subtitle && (
          <div className="sidebar-library-insights__card-subtitle">{subtitle}</div>
        )}
      </div>
      {clickable && (
        <ChevronRightIcon size={14} className="sidebar-library-insights__card-arrow" />
      )}
    </button>
  );
}

interface QuickCollectionProps {
  id: string;
  name: string;
  icon: React.ReactNode;
  count: number;
  color: string;
  onClick: () => void;
  isActive?: boolean;
}

function QuickCollection({ id, name, icon, count, color, onClick, isActive = false }: QuickCollectionProps) {
  return (
    <button
      type="button"
      className={`sidebar-library-insights__collection ${
        isActive ? "sidebar-library-insights__collection--active" : ""
      }`}
      onClick={onClick}
    >
      <div
        className="sidebar-library-insights__collection-icon"
        style={{ backgroundColor: color }}
      >
        {icon}
      </div>
      <div className="sidebar-library-insights__collection-content">
        <span className="sidebar-library-insights__collection-name">{name}</span>
        <span className="sidebar-library-insights__collection-count">{count}</span>
      </div>
    </button>
  );
}

export function SidebarLibraryInsights() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const { selectCollection, selectedCollection } = useLibraryCollections();
  const navigate = useNavigate();

  const handleLibraryNavigation = useCallback((collection?: string) => {
    // First navigate to library page
    navigate("/library");
    // Then set the collection (this will update the URL via the library page's useEffect)
    if (collection) {
      selectCollection(collection);
    } else {
      selectCollection(null);
    }
  }, [navigate, selectCollection]);

  // Calculate library statistics
  const libraryStats = useMemo(() => {
    const totalGames = library.length;
    const installedGames = library.filter(game => Boolean(game.executablePath)).length;
    const favoriteGames = library.filter(game => game.favorite).length;
    const recentlyPlayedGames = library.filter(game => wasPlayedRecently(game.lastTimePlayed)).length;
    const notPlayedGames = library.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0).length;
    
    const totalPlayTime = library.reduce((total, game) => {
      return total + (game.playTimeInMilliseconds || 0);
    }, 0);

    const averagePlayTime = totalGames > 0 ? totalPlayTime / totalGames : 0;

    return {
      totalGames,
      installedGames,
      favoriteGames,
      recentlyPlayedGames,
      notPlayedGames,
      totalPlayTime,
      averagePlayTime,
    };
  }, [library]);

  // Quick collections data
  const quickCollections = useMemo(() => [
    {
      id: "favorites",
      name: t("favorites"),
      icon: <StarIcon size={16} />,
      count: libraryStats.favoriteGames,
      color: "#ffc107",
    },
    {
      id: "recently-played",
      name: t("recently_played"),
      icon: <ClockIcon size={16} />,
      count: libraryStats.recentlyPlayedGames,
      color: "#3e62c0",
    },
    {
      id: "installed",
      name: t("installed"),
      icon: <DownloadIcon size={16} />,
      count: libraryStats.installedGames,
      color: "#1c9749",
    },
    {
      id: "not-played",
      name: t("not_played"),
      icon: <PlayIcon size={16} />,
      count: libraryStats.notPlayedGames,
      color: "#801d1e",
    },
  ], [libraryStats, t]);

  if (library.length === 0) {
    return null;
  }

  return (
    <section className="sidebar-library-insights">
      <div className="sidebar-library-insights__header">
        <GraphIcon size={16} />
        <h3 className="sidebar-library-insights__title">{t("library_insights")}</h3>
      </div>

      <div className="sidebar-library-insights__content">
        {/* Main Statistics */}
        <div className="sidebar-library-insights__stats">
          <InsightCard
            icon={<AppsIcon size={16} />}
            title={t("total_games")}
            value={libraryStats.totalGames}
            color="#6366f1"
            onClick={() => handleLibraryNavigation()}
            clickable
          />
          
          <InsightCard
            icon={<TrophyIcon size={16} />}
            title={t("total_playtime")}
            value={formatPlayTime(libraryStats.totalPlayTime)}
            subtitle={`${t("average")}: ${formatPlayTime(libraryStats.averagePlayTime)}`}
            color="#f59e0b"
          />
        </div>

        {/* Quick Collections */}
        <div className="sidebar-library-insights__section">
          <h4 className="sidebar-library-insights__section-title">
            {t("quick_collections")}
          </h4>
          <div className="sidebar-library-insights__collections">
            {quickCollections
              .filter(collection => collection.count > 0)
              .map((collection) => (
                <QuickCollection
                  key={collection.id}
                  {...collection}
                  onClick={() => handleLibraryNavigation(collection.id)}
                  isActive={selectedCollection === collection.id}
                />
              ))}
          </div>
        </div>

        {/* Library Health */}
        {libraryStats.totalGames > 0 && (
          <div className="sidebar-library-insights__section">
            <h4 className="sidebar-library-insights__section-title">
              {t("library_health")}
            </h4>
            <div className="sidebar-library-insights__health">
              <div className="sidebar-library-insights__health-item">
                <span className="sidebar-library-insights__health-label">
                  {t("installed")}
                </span>
                <div className="sidebar-library-insights__health-bar">
                  <div 
                    className="sidebar-library-insights__health-progress"
                    style={{ 
                      width: `${(libraryStats.installedGames / libraryStats.totalGames) * 100}%`,
                      backgroundColor: "#1c9749"
                    }}
                  />
                </div>
                <span className="sidebar-library-insights__health-value">
                  {Math.round((libraryStats.installedGames / libraryStats.totalGames) * 100)}%
                </span>
              </div>
              
              <div className="sidebar-library-insights__health-item">
                <span className="sidebar-library-insights__health-label">
                  {t("played")}
                </span>
                <div className="sidebar-library-insights__health-bar">
                  <div 
                    className="sidebar-library-insights__health-progress"
                    style={{ 
                      width: `${((libraryStats.totalGames - libraryStats.notPlayedGames) / libraryStats.totalGames) * 100}%`,
                      backgroundColor: "#3e62c0"
                    }}
                  />
                </div>
                <span className="sidebar-library-insights__health-value">
                  {Math.round(((libraryStats.totalGames - libraryStats.notPlayedGames) / libraryStats.totalGames) * 100)}%
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
