@use "../../scss/globals.scss";

.sidebar-library-insights {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  padding: calc(globals.$spacing-unit * 2);
  backdrop-filter: blur(24px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  margin-bottom: calc(globals.$spacing-unit * 2);

  // Animation for smooth appearance
  animation: insightsFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    margin-bottom: calc(globals.$spacing-unit * 2);
    padding-bottom: calc(globals.$spacing-unit);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  &__title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: globals.$muted-color;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__section {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__section-title {
    margin: 0;
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  // Statistics Cards
  &__stats {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__card {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    padding: calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 48px;

    &--clickable {
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.75);
    }
  }

  &__card-icon {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.2),
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 40px;
      height: 40px;
      min-width: 40px;
      min-height: 40px;
    }
  }

  &__card-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: calc(globals.$spacing-unit * 0.25);
  }

  &__card-value {
    font-size: 20px;
    font-weight: 700;
    color: globals.$muted-color;
    line-height: 1.1;
  }

  &__card-title {
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.4px;
    line-height: 1.2;
  }

  &__card-subtitle {
    font-size: 10px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.2;
  }

  &__card-arrow {
    color: rgba(255, 255, 255, 0.4);
    transition: all 0.2s ease;
  }

  &__card--clickable:hover &__card-arrow {
    color: rgba(255, 255, 255, 0.7);
    transform: translateX(2px);
  }

  // Quick Collections
  &__collections {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
  }

  &__collection {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.25);
    padding: calc(globals.$spacing-unit * 1.25) calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 48px;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &--active {
      background: rgba(255, 255, 255, 0.12);
      border-color: rgba(255, 255, 255, 0.25);
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 1.75);
    }
  }

  &__collection-icon {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow:
      0 3px 8px rgba(0, 0, 0, 0.25),
      0 1px 4px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 32px;
      height: 32px;
      min-width: 32px;
      min-height: 32px;
    }
  }

  &__collection-content {
    flex: 1;
    min-width: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__collection-name {
    font-size: 13px;
    font-weight: 600;
    color: globals.$muted-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
  }

  &__collection-count {
    font-size: 12px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.12);
    padding: calc(globals.$spacing-unit * 0.25) calc(globals.$spacing-unit * 0.5);
    border-radius: 6px;
    min-width: 24px;
    text-align: center;
    line-height: 1;
  }

  // Library Health
  &__health {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit);
  }

  &__health-item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
  }

  &__health-label {
    font-size: 11px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    min-width: 50px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
  }

  &__health-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 3px;
    overflow: hidden;
  }

  &__health-progress {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  &__health-value {
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    min-width: 30px;
    text-align: right;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding: calc(globals.$spacing-unit * 1.5);
    border-radius: 12px;
    
    &__card {
      min-height: 44px;
      padding: calc(globals.$spacing-unit);
    }

    &__card-icon {
      width: 28px;
      height: 28px;
      min-width: 28px;
      min-height: 28px;
    }

    &__card-value {
      font-size: 16px;
    }

    &__collection {
      min-height: 40px;
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit);
    }

    &__collection-icon {
      width: 20px;
      height: 20px;
      min-width: 20px;
      min-height: 20px;
    }
  }
}

@keyframes insightsFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
