import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  AppsIcon,
  StarIcon,
  ClockIcon,
  DownloadIcon,
  PlayIcon,
} from "@primer/octicons-react";

import { useLibrary, useLibraryCollections } from "@renderer/hooks";
import "./library-collection-header.scss";

interface CollectionInfo {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

export function LibraryCollectionHeader() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const { selectedCollection } = useLibraryCollections();

  // Get collection information
  const collectionInfo = useMemo((): CollectionInfo | null => {
    if (!selectedCollection) return null;

    const collections: Record<string, Omit<CollectionInfo, "id">> = {
      favorites: {
        name: t("favorites"),
        description: t("favorites_description"),
        icon: <StarIcon size={24} />,
        color: "#f59e0b",
      },
      "recently-played": {
        name: t("recently_played"),
        description: t("recently_played_description"),
        icon: <ClockIcon size={24} />,
        color: "#10b981",
      },
      installed: {
        name: t("installed"),
        description: t("installed_description"),
        icon: <DownloadIcon size={24} />,
        color: "#1c9749",
      },
      "not-played": {
        name: t("not_played"),
        description: t("not_played_description"),
        icon: <PlayIcon size={24} />,
        color: "#dc2626",
      },
    };

    const collection = collections[selectedCollection];
    if (!collection) {
      return {
        id: selectedCollection,
        name: selectedCollection,
        description: t("custom_collection_description"),
        icon: <AppsIcon size={24} />,
        color: "#6366f1",
      };
    }

    return {
      id: selectedCollection,
      ...collection,
    };
  }, [selectedCollection, t]);

  // Calculate game counts
  const gameCounts = useMemo(() => {
    if (!selectedCollection) return null;

    let filteredGames = library;

    switch (selectedCollection) {
      case "favorites":
        filteredGames = library.filter(game => game.isFavorite);
        break;
      case "recently-played":
        filteredGames = library.filter(game => 
          game.lastTimePlayed && 
          Date.now() - new Date(game.lastTimePlayed).getTime() < 7 * 24 * 60 * 60 * 1000
        );
        break;
      case "installed":
        filteredGames = library.filter(game => Boolean(game.executablePath));
        break;
      case "not-played":
        filteredGames = library.filter(game => !game.lastTimePlayed);
        break;
      default:
        // For custom collections, we'd need to implement collection filtering
        filteredGames = library;
        break;
    }

    return {
      filtered: filteredGames.length,
      total: library.length,
    };
  }, [selectedCollection, library]);

  if (!collectionInfo || !gameCounts) {
    return null;
  }

  return (
    <div className="library-collection-header">
      <div className="library-collection-header__content">
        <div className="library-collection-header__icon-section">
          <div 
            className="library-collection-header__icon"
            style={{ backgroundColor: collectionInfo.color }}
          >
            {collectionInfo.icon}
          </div>
        </div>
        
        <div className="library-collection-header__info">
          <h2 className="library-collection-header__title">
            {collectionInfo.name}
          </h2>
          <p className="library-collection-header__description">
            {collectionInfo.description}
          </p>
          <div className="library-collection-header__stats">
            <span className="library-collection-header__count">
              {gameCounts.filtered} {gameCounts.filtered === 1 ? t("game") : t("games")}
            </span>
            {gameCounts.filtered !== gameCounts.total && (
              <span className="library-collection-header__total">
                {t("of")} {gameCounts.total} {t("total")}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
