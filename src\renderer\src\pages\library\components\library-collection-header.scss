@use "../../../scss/globals.scss";

.library-collection-header {
  margin-bottom: calc(globals.$spacing-unit * 3);
  animation: headerSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &__content {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 16px;
    backdrop-filter: blur(24px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);

    // Responsive design
    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: calc(globals.$spacing-unit * 1.5);
      padding: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__icon-section {
    flex-shrink: 0;
  }

  &__icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 
      0 8px 24px rgba(0, 0, 0, 0.3),
      0 4px 12px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    @media (max-width: 768px) {
      width: 56px;
      height: 56px;
    }
  }

  &__info {
    flex: 1;
    min-width: 0;

    @media (max-width: 768px) {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  &__title {
    margin: 0 0 calc(globals.$spacing-unit * 0.5) 0;
    font-size: 24px;
    font-weight: 700;
    color: globals.$muted-color;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 20px;
    }
  }

  &__description {
    margin: 0 0 calc(globals.$spacing-unit) 0;
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;

    @media (max-width: 768px) {
      font-size: 13px;
    }
  }

  &__stats {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  &__count {
    font-size: 14px;
    font-weight: 700;
    color: globals.$muted-color;
    background: rgba(255, 255, 255, 0.1);
    padding: calc(globals.$spacing-unit * 0.25) calc(globals.$spacing-unit * 0.75);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  &__total {
    font-size: 13px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
  }
}

// Animation keyframes
@keyframes headerSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
